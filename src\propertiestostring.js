const parser = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const generate = require('@babel/generator').default;
const t = require('@babel/types');

/**
 * Converts object properties from identifier and numeric notation to string notation
 * Example: {test: true, 123: false} -> {"test": true, "123": false}
 * @param {string} code - JavaScript code to process
 * @returns {Object} - Object containing transformed code and statistics
 */
function processPropertiesToString(code) {
    let transformedCount = 0;
    
    try {
        const ast = parser.parse(code, {
            sourceType: 'module',
            allowImportExportEverywhere: true,
            allowReturnOutsideFunction: true,
            plugins: [
                'jsx',
                'typescript',
                'decorators-legacy',
                'classProperties',
                'objectRestSpread',
                'functionBind',
                'exportDefaultFrom',
                'exportNamespaceFrom',
                'dynamicImport',
                'nullishCoalescingOperator',
                'optionalChaining'
            ]
        });

        traverse(ast, {
            ObjectProperty(path) {
                const node = path.node;

                // Only transform if the key is an identifier or numeric literal and not computed
                if (!node.computed) {
                    if (t.isIdentifier(node.key)) {
                        // Convert identifier key to string literal
                        const stringKey = t.stringLiteral(node.key.name);
                        node.key = stringKey;
                        transformedCount++;
                    } else if (t.isNumericLiteral(node.key)) {
                        // Convert numeric key to string literal
                        const stringKey = t.stringLiteral(node.key.value.toString());
                        node.key = stringKey;
                        transformedCount++;
                    }
                }
            },
            
            ObjectMethod(path) {
                const node = path.node;

                // Only transform if the key is an identifier or numeric literal and not computed
                if (!node.computed) {
                    if (t.isIdentifier(node.key)) {
                        // Convert identifier key to string literal
                        const stringKey = t.stringLiteral(node.key.name);
                        node.key = stringKey;
                        transformedCount++;
                    } else if (t.isNumericLiteral(node.key)) {
                        // Convert numeric key to string literal
                        const stringKey = t.stringLiteral(node.key.value.toString());
                        node.key = stringKey;
                        transformedCount++;
                    }
                }
            }
        });

        const transformedCode = generate(ast, {
            compact: false,
            minified: false
        }).code;

        return {
            code: transformedCode,
            transformedCount,
            success: true
        };
    } catch (error) {
        console.error('Error processing properties to string:', error);
        return {
            code,
            transformedCount: 0,
            success: false,
            error: error.message
        };
    }
}

module.exports = {
    processPropertiesToString
};