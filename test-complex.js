const { obfuscateCode } = require('./src/obfuscator');

// More complex test with various object property scenarios
const complexTestCode = `
const config = {
    "apiUrl": "https://api.example.com",
    "timeout": 5000,
    "headers": {
        "Content-Type": "application/json",
        "Authorization": "Bearer token123"
    },
    "retryCount": 3
};

class MyClass {
    constructor() {
        this.data = {
            "name": "test",
            "value": "some string value"
        };
    }
    
    ["dynamicMethod"]() {
        return "dynamic method result";
    }
}

const obj = {
    "method1": function() {
        return "method1 result";
    },
    "method2": () => {
        return "method2 result";
    },
    "prop": "property value"
};

// Regular string literals (should be obfuscated)
console.log("This should be obfuscated");
const message = "Another string to obfuscate";

// Computed properties (should be obfuscated)
const key = "dynamicKey";
const dynamicObj = {
    [key]: "dynamic value",
    ["computed" + "Key"]: "computed value"
};
`;

async function testComplexFix() {
    try {
        console.log('Testing complex obfuscation scenarios...');
        console.log('Original code length:', complexTestCode.length);
        
        const obfuscated = await obfuscateCode(complexTestCode, 'browser');
        
        console.log('✅ Success! Complex obfuscation completed without errors.');
        console.log('Obfuscated code length:', obfuscated.length);
        console.log('\nFirst 800 characters of obfuscated code:');
        console.log(obfuscated.substring(0, 800) + '...');
        
    } catch (error) {
        console.error('❌ Complex test failed:', error.message);
        console.error('Stack trace:', error.stack);
    }
}

testComplexFix();
