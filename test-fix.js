const { obfuscateCode } = require('./src/obfuscator');

// Test code that contains object properties with string literal keys
// This is the type of code that was causing the error
const testCode = `
const obj = {
    "key1": "value1",
    "key2": "value2",
    "method": function() {
        return "hello world";
    }
};

const anotherObj = {
    "test": "string literal",
    "number": 42,
    "nested": {
        "inner": "nested string"
    }
};

console.log(obj["key1"]);
console.log("This is a regular string");
`;

async function testFix() {
    try {
        console.log('Testing obfuscation with object property string literals...');
        console.log('Original code length:', testCode.length);
        
        const obfuscated = await obfuscateCode(testCode, 'browser');
        
        console.log('✅ Success! Obfuscation completed without errors.');
        console.log('Obfuscated code length:', obfuscated.length);
        console.log('\nFirst 500 characters of obfuscated code:');
        console.log(obfuscated.substring(0, 500) + '...');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        console.error('Stack trace:', error.stack);
    }
}

testFix();
